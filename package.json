{"name": "adds-ai-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@fontsource/poppins": "^5.2.6", "@heroicons/react": "^2.2.0", "@prisma/client": "^5.0.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@types/js-cookie": "^3.0.6", "@types/xml2js": "^0.4.14", "bcrypt": "^5.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.0", "lucide-react": "^0.514.0", "next": "^15.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tinycolor2": "^1.6.0", "tw-animate-css": "^1.3.4", "xml2js": "^0.6.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.4.5", "@types/react": "^18.2.15", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.24", "prisma": "^5.0.0", "tailwindcss": "^3.3.2", "typescript": "^5.8.3"}}