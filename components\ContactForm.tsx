"use client";
import React, { useState } from "react";

interface ContactFormProps {
	siteData: SiteData;
}

export default function ContactForm({ siteData }: ContactFormProps) {
	const [form, setForm] = useState({
		name: "",
		email: "",
		subject: "",
		message: "",
		siteId: siteData.id,
	});
	const [loading, setLoading] = useState(false);
	const [success, setSuccess] = useState<string | null>(null);
	const [error, setError] = useState<string | null>(null);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		
		setForm({ ...form, [e.target.name]: e.target.value });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setLoading(true);
		setSuccess(null);
		setError(null);

		if (!form.name || !form.email || !form.subject || !form.message) {
			setError("Name, email, subject, and message are required.");
			setLoading(false);
			return;
		}

		if(form.email && !/\S+@\S+\.\S+/.test(form.email)) {
			setError("Please enter a valid email address.");
			setLoading(false);
			return;
		}
		if (form.subject.length > 100) {
			setError("Subject must be less than 100 characters.");
			setLoading(false);
			return;
		}
		if (form.name.length > 50) {
			setError("Name must be less than 50 characters.");
			setLoading(false);
			return;
		}
		if (form.email.length > 100) {
			setError("Email must be less than 100 characters.");
			setLoading(false);
			return;
		}
		if (form.subject.length > 100) {
			setError("Subject must be less than 100 characters.");
			setLoading(false);
			return;
		}
		if (form.message.length < 10) {
			setError("Message must be at least 10 characters long.");
			setLoading(false);
			return;
		}
		if (form.message.length > 500) {
			setError("Message must be less than 500 characters.");
			setLoading(false);
			return;
		}

		try {
			const apiUrl = process.env.API_URL || "http://localhost:3000";
			const res = await fetch(`/api/contact/submit`, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(form),
			});
			if (res.ok) {
				setSuccess("Your message has been sent!");
				setForm({ name: "", email: "", subject: "", message: "", siteId: siteData.id });
			} else {
				const data = await res.json();
				setError(data.error || "Failed to send message.");
			}
		} catch (err) {
			console.log("Error submitting contact form:", err);
			setError("Failed to send message.");
		} finally {
			setLoading(false);

		};
	}

	return (
		<form className="space-y-4" onSubmit={handleSubmit}>
			<input
				type="text"
				name="name"
				placeholder="Your Name*"
				className="w-full rounded-lg border border-gray-200 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
				required={true}
				value={form.name}
				onChange={handleChange}
			/>
			<input
				type="email"
				name="email"
				placeholder="Your Email*"
				className="w-full rounded-lg border border-gray-200 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
				required={true}
				value={form.email}
				onChange={handleChange}
			/>
			<input
				type="text"
				name="subject"
				placeholder="Your Subject"
				className="w-full rounded-lg border border-gray-200 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
				required={true}
				value={form.subject}
				onChange={handleChange}
			/>
			<textarea
				name="message"
				placeholder="Your Message"
				className="w-full rounded-lg border border-gray-200 px-4 py-2 min-h-[100px] focus:outline-none focus:ring-2 focus:ring-primary"
				required={true}
				value={form.message}
				onChange={handleChange}
			/>
			<button
				type="submit"
				className="bg-primary hover:bg-primary-600 text-white font-medium px-6 py-2 rounded-lg shadow"
				disabled={loading}
			>
				{loading ? "Sending..." : "Send Message"}
			</button>
			{success && <div className="text-green-600">{success}</div>}
			{error && <div className="text-red-600">{error}</div>}
		</form>
	);
}